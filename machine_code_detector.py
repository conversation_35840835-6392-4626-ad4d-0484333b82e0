#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码检测工具
用于在打包前获取目标机器的唯一机器码
"""

import uuid
import hashlib
import os
import platform
import subprocess
import sys

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QPushButton, QMessageBox,
                            QGroupBox, QFrame)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QClipboard

class MachineCodeDetector:
    def __init__(self):
        self.machine_id = self._get_machine_id()

    def _get_machine_id(self):
        """生成基于多种硬件信息的稳定唯一机器码（与主程序保持一致）"""
        machine_info = []
        
        try:
            # 获取主板序列号
            if os.name == "nt":  # Windows
                try:
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取CPU序列号
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'ProcessorId' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取硬盘序列号
                try:
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line and line.strip() != '(null)':
                                machine_info.append(line.strip())
                                break  # 只取第一个硬盘
                except:
                    pass
                    
            elif os.name == "posix":  # Linux/Mac
                try:
                    # 尝试获取机器ID
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                    elif os.path.exists('/var/lib/dbus/machine-id'):
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                except:
                    pass
                    
                # 获取CPU信息
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'serial' in line.lower():
                                machine_info.append(line.split(':')[1].strip())
                                break
                except:
                    pass
        except:
            pass
        
        # 获取MAC地址作为备用
        try:
            mac = uuid.getnode()
            machine_info.append(str(mac))
        except:
            pass
        
        # 获取系统信息作为额外标识
        try:
            machine_info.append(platform.machine())
            machine_info.append(platform.processor())
        except:
            pass
        
        # 如果没有获取到任何信息，使用UUID作为备用
        if not machine_info:
            machine_info.append(str(uuid.uuid4()))
        
        # 组合所有信息并生成哈希
        combined_info = ''.join(machine_info)
        return hashlib.sha256(combined_info.encode()).hexdigest()[:32]  # 取前32位

class MachineCodeDetectorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.detector = MachineCodeDetector()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("机器码检测工具")
        self.setGeometry(200, 200, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("机器码检测工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 机器码显示区域
        machine_code_group = QGroupBox("机器码")
        machine_code_layout = QVBoxLayout(machine_code_group)
        
        self.machine_code_label = QLabel(f"当前机器码: {self.detector.machine_id}")
        self.machine_code_label.setWordWrap(True)
        self.machine_code_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc; }")
        machine_code_layout.addWidget(self.machine_code_label)
        
        # 复制按钮
        copy_layout = QHBoxLayout()
        copy_button = QPushButton("复制机器码")
        copy_button.clicked.connect(self.copy_machine_code)
        copy_layout.addWidget(copy_button)
        copy_layout.addStretch()
        machine_code_layout.addLayout(copy_layout)
        
        layout.addWidget(machine_code_group)
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.detector.machine_id)
        QMessageBox.information(self, "成功", "机器码已复制到剪贴板！")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("机器码检测工具")
    app.setApplicationVersion("1.0")
    
    window = MachineCodeDetectorGUI()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
