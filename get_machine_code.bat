@echo off
chcp 65001 >nul
echo ========================================
echo           机器码检测工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查PySide6是否安装
python -c "import PySide6" >nul 2>&1
if errorlevel 1 (
    echo 警告：未找到PySide6，正在尝试安装...
    pip install PySide6
    if errorlevel 1 (
        echo 错误：PySide6安装失败
        pause
        exit /b 1
    )
)

echo 正在启动机器码检测工具...
echo.
python machine_code_detector.py

if errorlevel 1 (
    echo.
    echo 错误：程序运行失败
    pause
    exit /b 1
)

echo.
echo 程序已关闭
pause
