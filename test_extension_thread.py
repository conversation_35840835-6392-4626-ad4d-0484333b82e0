#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扩展功能线程是否正常工作
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QProgressBar
from PySide6.QtCore import QThread, Signal, QTimer

# 简化的扩展功能线程测试
class TestExtensionThread(QThread):
    log_signal = Signal(str)
    step_progress_signal = Signal(int)
    finished_signal = Signal(list)
    
    def __init__(self, test_files):
        super().__init__()
        self.test_files = test_files
        self.is_running = True
        
    def run(self):
        """模拟扩展功能执行"""
        try:
            self.log_signal.emit("开始执行扩展功能测试")
            
            # 模拟4个扩展功能
            extensions = ["转场", "抽帧去重", "背景音乐", "批量重命名"]
            
            for i, ext_name in enumerate(extensions):
                if not self.is_running:
                    break
                    
                self.log_signal.emit(f"正在执行：{ext_name}")
                
                # 模拟处理时间
                for j in range(10):
                    if not self.is_running:
                        break
                    self.msleep(100)  # 模拟处理时间
                    
                self.log_signal.emit(f"✅ {ext_name} 完成")
                self.step_progress_signal.emit(i + 1)
                
            self.finished_signal.emit(self.test_files)
            
        except Exception as e:
            self.log_signal.emit(f"❌ 测试异常：{str(e)}")
            
    def stop(self):
        """停止处理"""
        self.is_running = False

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("扩展功能线程测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 开始按钮
        self.start_button = QPushButton("开始测试扩展功能")
        self.start_button.clicked.connect(self.start_test)
        layout.addWidget(self.start_button)
        
        # 停止按钮
        self.stop_button = QPushButton("停止测试")
        self.stop_button.clicked.connect(self.stop_test)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 4)  # 4个扩展功能
        layout.addWidget(self.progress_bar)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 扩展功能线程
        self.extension_thread = None
        
    def start_test(self):
        """开始测试"""
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 模拟测试文件
        test_files = ["test1.mp4", "test2.mp4", "test3.mp4"]
        
        # 创建并启动扩展功能线程
        self.extension_thread = TestExtensionThread(test_files)
        self.extension_thread.log_signal.connect(self.update_log)
        self.extension_thread.step_progress_signal.connect(self.update_progress)
        self.extension_thread.finished_signal.connect(self.on_finished)
        self.extension_thread.start()
        
    def stop_test(self):
        """停止测试"""
        if self.extension_thread and self.extension_thread.isRunning():
            self.extension_thread.stop()
            self.extension_thread.terminate()
            self.update_log("❌ 测试已停止")
            
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        
    def update_progress(self, step):
        """更新进度"""
        self.progress_bar.setValue(step)
        
    def on_finished(self, files):
        """测试完成"""
        self.update_log("🎉 所有扩展功能测试完成！")
        self.update_log(f"处理的文件：{files}")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
