#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带授权验证的主程序示例
展示如何在你的main.py中集成机器码授权系统
"""

import sys
import io

# 在打包后的exe中，重定向stdout和stderr以避免弹出控制台窗口
if getattr(sys, 'frozen', False):
    # 如果是打包后的exe，重定向输出到空设备
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()
else:
    # 开发环境下正常配置编码
    if sys.stdout is not None:
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr is not None:
        sys.stderr.reconfigure(encoding='utf-8')

import os
from machine_code_verifier import run_application_with_authorization_check

def main_application():
    """
    你的主应用程序函数
    这里是原来main.py中的主要逻辑
    """
    # 导入你的主要模块
    from PySide6.QtWidgets import QApplication, QMainWindow
    from ui_main_window import Ui_MainWindow
    # 导入其他必要的模块...
    
    print("授权验证通过，启动主应用程序...")
    
    # 创建应用程序实例
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(window)
    
    # 这里可以添加你原来main.py中的其他初始化代码
    # 例如：
    # - 设置窗口属性
    # - 连接信号和槽
    # - 初始化数据
    # - 等等...
    
    # 显示窗口
    window.show()
    
    # 运行应用程序
    return app.exec()

def main():
    """
    主入口函数，带授权检查
    """
    # 使用机器码授权检查来启动应用程序
    # 如果机器码不在授权列表中，会显示授权对话框并阻止程序运行
    # 如果机器码在授权列表中，会正常启动主应用程序
    
    try:
        result = run_application_with_authorization_check(main_application, "MFChen视频混剪工具")
        return result
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
