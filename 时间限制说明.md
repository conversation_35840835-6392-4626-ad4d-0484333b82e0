# 授权码时间限制方案（本地时间验证版本）

## 一、核心设计目标（已更新）
- **时间有效性验证**：使用本地时间验证授权码是否过期
- **防时间篡改**：检测本地时间是否被人为修改，避免绕过时间限制
- **时间回滚检测**：如果本地时间突然回退12小时以上，触发授权异常A
- **异常统一提示**：通过「异常码 + 字母」标识不同错误类型，对外统一提示格式

## 重要更新说明
**本版本已完全移除网络时间验证功能**，改为纯本地时间验证机制，解决网络访问问题。


## 二、核心机制架构（已更新）
```
本地时间模块 --> 获取当前本地时间
    |
    v
历史记录模块 --> 加载/保存时间历史记录
    |
    v
时间验证模块 --> 检查时间回滚（12小时阈值）
    |           --> 检查时间一致性
    |           --> 检查使用频率异常
    v
授权验证模块 --> 验证授权是否过期
    |
    v
错误处理模块 --> 返回异常码A（时间相关错误）
```

## 三、关键技术点详解（已更新）

### 1. 本地时间记录机制
**新的数据格式：**
```json
{
  "history": [
    {
      "timestamp": 1749797902,
      "access_time": 1749797902,
      "sequence": 1,
      "hash": "eee902e20f5be104"
    }
  ],
  "first_use": 1749797902,
  "last_use": 1749797902,
  "access_count": 1
}
```

**记录策略：**
- 每次使用时记录当前本地时间
- 保留最近10条记录（增加检测精度）
- 使用序列号确保记录的连续性
- HMAC-SHA256哈希防篡改

### 2. 异常代码体系（已简化）

| 异常类型 | 代码前缀 | 触发条件 | 对外提示格式 |
|---------|---------|---------|-------------|
| 时间相关异常 | A | 授权时间过期 / 时间回滚超过12小时 / 时间一致性检查失败 / 使用频率异常 | 授权异常 XXXA，请重新授权 |

**注意：** 本版本已移除B、C、D类异常，统一使用A类异常处理所有时间相关问题。

### 3. 本地时间处理流程（已更新）

```
启动验证 →
    1. 获取当前本地时间
    2. 加载历史记录
    3. 检查时间回滚（阈值：12小时）
    ├─ 检测到回滚 → 异常码A
    └─ 未检测到回滚 →
        4. 检查时间一致性
        ├─ 一致性检查失败 → 异常码A
        └─ 一致性检查通过 →
            5. 检查授权是否过期
            ├─ 已过期 → 异常码A
            └─ 未过期 →
                6. 检查使用频率异常
                ├─ 频率异常 → 异常码A
                └─ 正常 → 验证通过
```

### 4. 安全加固措施（已更新）

**数据加密：** 本地时间记录使用 Fernet 加密，防止直接篡改

**哈希校验：** 每条记录添加 HMAC-SHA256 校验值
```
格式：HMAC(timestamp + access_time + sequence, 密钥)
```

**时间回滚防御：**
- 阈值：12小时（可配置）
- 检测：当前时间 < 上次使用时间 - 12小时 → 触发异常码A

**使用频率检测：**
- 检测最近5次使用间隔
- 全部小于30秒 → 触发异常码A

## 四、用户交互与异常提示（已更新）

**统一提示模板：** 授权异常{三位随机数}A，请重新授权

**示例：**
- 时间过期 → 「授权异常 685A，请重新授权」
- 时间回滚 → 「授权异常 927A，请重新授权」
- 一致性异常 → 「授权异常 341A，请重新授权」

**隐藏逻辑：** 提示语不暴露具体异常原因，统一使用A类异常

## 五、极端场景兜底方案（已更新）

**首次使用：** 记录本地时间作为基准，建立初始时间记录

**历史记录损坏：** 若加密文件无法解析或哈希校验失败，重新初始化记录

**时间记录清空：** 视为首次使用，重新建立时间基准

## 六、版本更新总结

**主要变更：**
1. ✅ 完全移除网络时间验证
2. ✅ 改为纯本地时间验证机制
3. ✅ 简化异常代码体系（只保留A类）
4. ✅ 增强时间回滚检测（12小时阈值）
5. ✅ 添加使用频率异常检测
6. ✅ 兼容旧格式历史记录

**解决的问题：**
- ❌ 网络访问问题（已完全移除网络依赖）
- ✅ 时间篡改检测（通过本地时间回滚检测）
- ✅ 数据完整性保护（加密存储+哈希校验）