# 授权码时间限制方案（结构化解析）

一、核心设计目标
时间有效性验证：通过网络时间与本地时间比对，判断授权码是否过期
防时间篡改：检测本地时间是否被人为修改，避免绕过时间限制
无网络容错：服务器不可访问时，基于历史记录实现本地时间可信性判断
异常统一提示：通过「异常码 + 字母」标识不同错误类型，对外统一提示格式


二、核心机制架构
graph TD
A[时间源模块] -->|联网时| B[获取网络时间+本地时间]
A -->|无网络时| C[读取本地历史时间记录]
D[授权验证模块] --> E{验证时间有效性}
E -->|过期| F[返回异常码A]
E -->|本地时间篡改| G[返回异常码B]
E -->|无有效记录| H[返回异常码C]
I[数据存储模块] --> J[加密存储时间比对记录]
I --> K[哈希校验防篡改]


三、关键技术点详解
1. 双时间源比对机制
联网时操作：
同步获取 NTP 服务器时间（如阿里云 / 百度）与本地系统时间
计算时间差 ΔT = 本地时间 - 网络时间，存储最近 3 次记录（含时间戳、ΔT、HMAC 哈希）
数据格式示例：
{
  "history": [
    {"net":1686000000,"local":1686000005,"delta":5,"hash":"e10adc3949ba59abbe56e057f20f883e"},
    {"net":1686008640,"local":1686008643,"delta":3,"hash":"a21e9821a537d5c2"}
  ]
}

2. 异常代码体系（核心标识）
异常类型	代码前缀	触发条件	对外提示格式
授权时间过期	A	有效时间计算后超过授权截止时间	授权异常 XXXAXXX，请重新授权
本地时间篡改	B	本地时间与历史网络时间偏差 > 30 分钟，或检测到时间回滚（当前时间 < 上次使用时间）	授权异常 XXXBXXX，请重新授权
无有效时间记录	C	首次使用未联网，或历史记录超过 30 天未更新	授权异常 XXXCXXX，请重新授权
服务器全失效	D	所有时间服务器无法访问且本地时间验证失败	授权异常 XXXDXXX，请重新授权


3. 无网络时的时间处理流程
        无网络 → 
            1. 检查历史记录有效性（近7天内且ΔT波动≤5分钟）
            ├─ 有效：
            │  ├─ 计算当前有效时间 = 授权网络时间 + (当前本地时间 - 授权时本地时间)
            │  └─ 若有效时间 > 截止时间 → 异常码A
            └─ 无效：
                ├─ 检查时间回滚（当前时间 < 上次时间且差>1天）→ 是→异常码B
                ├─ 检查首次使用记录（T0）：
                │  ├─ 有且当前时间-T0>7天 → 异常码A
                │  └─ 无→记录T0，暂不校验
                └─ 本地时间偏差>30分钟 → 异常码B

4. 安全加固措施
数据加密：本地时间记录使用 AES-256 加密，防止直接篡改
哈希校验：每条记录添加 HMAC-SHA256 校验值，格式为HMAC(net+local+delta, 密钥)
时间回滚防御：记录上次使用时间 T_last，若当前时间 <T_last 且差> 24h→触发异常码 B


四、用户交互与异常提示
统一提示模板：授权异常{三位随机数}{字母}，请重新授权
（例：时间过期→「授权异常 685A，请重新授权」，时间篡改→「授权异常 927B，请重新授权」）
隐藏逻辑：提示语不暴露时间校验细节，仅通过字母前缀供内部定位异常类型


五、极端场景兜底方案
首次使用无网络：记录本地时间 T0，允许 7 天内使用，超过则触发异常码 A
服务器全失效：优先使用通过历史 ΔT 验证的本地时间，验证失败则返回异常码 D
时间记录损坏：若加密文件无法解析或哈希校验失败，直接返回异常码 C
该方案通过「网络时间比对 + 本地时间可信性验证 + 异常代码标识」三层机制，实现授权时间限制的同时，防止时间篡改绕过，并在无网络场景下通过历史记录实现容错，所有异常对外统一提示格式，内部通过代码前缀快速定位问题类型。