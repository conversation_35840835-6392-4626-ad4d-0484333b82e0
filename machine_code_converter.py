#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码转换工具
将机器码转换为独特的MD5值，增加验证复杂度
"""

import hashlib
import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                              QWidget, QLabel, QTextEdit, QPushButton, QMessageBox,
                              QGroupBox, QLineEdit)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class MachineCodeConverter:
    def __init__(self):
        # 密钥盐值 - 用于增加MD5复杂度，可以自定义
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"
        
    def convert_machine_code_to_md5(self, machine_code):
        """
        将机器码转换为独特的MD5值
        使用自定义算法增加复杂度
        """
        if not machine_code:
            return ""
        
        # 第一步：机器码 + 盐值
        step1 = machine_code + self.salt_key
        
        # 第二步：反转字符串
        step2 = step1[::-1]
        
        # 第三步：插入额外字符
        step3 = ""
        for i, char in enumerate(step2):
            step3 += char
            if i % 3 == 0:
                step3 += str(i % 10)
        
        # 第四步：再次加盐并生成MD5
        final_string = step3 + self.salt_key + machine_code
        md5_hash = hashlib.md5(final_string.encode('utf-8')).hexdigest()
        
        return md5_hash.upper()  # 返回大写MD5
    
    def batch_convert(self, machine_codes):
        """批量转换机器码"""
        results = {}
        for code in machine_codes:
            if code.strip():
                results[code.strip()] = self.convert_machine_code_to_md5(code.strip())
        return results

class MachineCodeConverterGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.converter = MachineCodeConverter()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("机器码转换工具")
        self.setGeometry(200, 200, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("机器码转换工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文本
        desc_label = QLabel("将机器码转换为授权码，用于增强授权验证的安全性")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("QLabel { color: #666; margin: 10px; }")
        layout.addWidget(desc_label)
        
        # 单个转换区域
        single_group = QGroupBox("单个机器码转换")
        single_layout = QVBoxLayout(single_group)
        
        # 输入框
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("机器码:"))
        self.single_input = QLineEdit()
        self.single_input.setPlaceholderText("请输入机器码...")
        input_layout.addWidget(self.single_input)
        
        convert_button = QPushButton("转换")
        convert_button.clicked.connect(self.convert_single)
        input_layout.addWidget(convert_button)
        
        single_layout.addLayout(input_layout)
        
        # 结果显示
        self.single_result = QTextEdit()
        self.single_result.setMaximumHeight(100)
        self.single_result.setReadOnly(True)
        single_layout.addWidget(self.single_result)
        
        layout.addWidget(single_group)
        
        # 批量转换区域
        batch_group = QGroupBox("批量机器码转换")
        batch_layout = QVBoxLayout(batch_group)
        
        # 输入区域
        batch_layout.addWidget(QLabel("机器码列表（每行一个）:"))
        self.batch_input = QTextEdit()
        self.batch_input.setMaximumHeight(150)
        self.batch_input.setPlaceholderText("请输入机器码，每行一个...\n例如：\nb837e25899a1d51becdd9fd0bb39bec6\na1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6")
        batch_layout.addWidget(self.batch_input)
        
        # 转换按钮
        batch_convert_layout = QHBoxLayout()
        batch_convert_button = QPushButton("批量转换")
        batch_convert_button.clicked.connect(self.convert_batch)
        batch_convert_layout.addWidget(batch_convert_button)
        
        clear_button = QPushButton("清空")
        clear_button.clicked.connect(self.clear_all)
        batch_convert_layout.addWidget(clear_button)
        
        batch_convert_layout.addStretch()
        batch_layout.addLayout(batch_convert_layout)
        
        # 结果显示
        batch_layout.addWidget(QLabel("转换结果:"))
        self.batch_result = QTextEdit()
        self.batch_result.setReadOnly(True)
        batch_layout.addWidget(self.batch_result)
        
        layout.addWidget(batch_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        copy_button = QPushButton("复制结果")
        copy_button.clicked.connect(self.copy_results)
        button_layout.addWidget(copy_button)
        
        save_button = QPushButton("保存到文件")
        save_button.clicked.connect(self.save_to_file)
        button_layout.addWidget(save_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)

    def convert_single(self):
        """转换单个机器码"""
        machine_code = self.single_input.text().strip()
        if not machine_code:
            QMessageBox.warning(self, "警告", "请输入机器码！")
            return

        auth_code = self.converter.convert_machine_code_to_md5(machine_code)

        result_text = f"机器码: {machine_code}\n授权码: {auth_code}\n\n用于代码中的格式:\n\"{auth_code}\","
        self.single_result.setPlainText(result_text)

    def convert_batch(self):
        """批量转换机器码"""
        input_text = self.batch_input.toPlainText().strip()
        if not input_text:
            QMessageBox.warning(self, "警告", "请输入机器码列表！")
            return
        
        machine_codes = [line.strip() for line in input_text.split('\n') if line.strip()]
        if not machine_codes:
            QMessageBox.warning(self, "警告", "没有找到有效的机器码！")
            return
        
        results = self.converter.batch_convert(machine_codes)
        
        # 格式化结果
        result_text = "转换结果:\n" + "="*50 + "\n\n"

        for machine_code, auth_code in results.items():
            result_text += f"机器码: {machine_code}\n授权码: {auth_code}\n\n"

        result_text += "用于代码中的格式:\n" + "-"*30 + "\n"
        result_text += "authorized_config = {\n"
        for auth_code in results.values():
            result_text += f"    \"{auth_code}\": {{\n"
            result_text += f"        \"expire_date\": \"2025-12-31\",\n"
            result_text += f"        \"description\": \"用户授权\"\n"
            result_text += f"    }},\n"
        result_text += "}\n"
        
        self.batch_result.setPlainText(result_text)

    def clear_all(self):
        """清空所有内容"""
        self.single_input.clear()
        self.single_result.clear()
        self.batch_input.clear()
        self.batch_result.clear()

    def copy_results(self):
        """复制结果到剪贴板"""
        batch_text = self.batch_result.toPlainText()
        single_text = self.single_result.toPlainText()
        
        if batch_text:
            clipboard = QApplication.clipboard()
            clipboard.setText(batch_text)
            QMessageBox.information(self, "成功", "批量转换结果已复制到剪贴板！")
        elif single_text:
            clipboard = QApplication.clipboard()
            clipboard.setText(single_text)
            QMessageBox.information(self, "成功", "单个转换结果已复制到剪贴板！")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的结果！")

    def save_to_file(self):
        """保存结果到文件"""
        batch_text = self.batch_result.toPlainText()
        single_text = self.single_result.toPlainText()
        
        content = ""
        if batch_text:
            content = batch_text
        elif single_text:
            content = single_text
        else:
            QMessageBox.warning(self, "警告", "没有可保存的结果！")
            return
        
        try:
            with open("machine_code_md5_results.txt", "w", encoding="utf-8") as f:
                f.write(content)
            QMessageBox.information(self, "成功", "结果已保存到 machine_code_md5_results.txt 文件！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存文件失败：{str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("机器码转换工具")
    app.setApplicationVersion("1.0")
    
    window = MachineCodeConverterGUI()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
