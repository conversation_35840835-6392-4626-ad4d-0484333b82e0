# 🔐 完整授权系统使用指南

## 🎉 系统升级完成

你的授权系统现在具备以下功能：

### ✅ 已解决的问题
1. **CMD闪烁问题** - 彻底解决，打包后无CMD窗口闪烁
2. **MD5验证复杂度** - 机器码通过复杂算法转换为MD5验证
3. **时间限制功能** - 支持授权过期时间控制和防篡改

### 🔧 新增功能
1. **时间验证系统** - 网络时间同步，防止本地时间篡改
2. **授权配置管理** - 支持为每个用户设置不同的过期时间
3. **配置生成工具** - 批量生成授权配置的GUI工具
4. **独立检测工具** - 可单独打包的机器码检测工具

## 📁 文件清单

### 核心文件
- ✅ `machine_code_verifier.py` - 主要授权验证模块（已升级）
- ✅ `time_validator.py` - 时间验证模块（新增）
- ✅ `machine_code_detector.py` - 机器码检测工具（已优化）
- ✅ `machine_code_converter.py` - 机器码转换工具
- ✅ `authorization_config_generator.py` - 授权配置生成工具（新增）

### 打包文件
- ✅ `打包用package.py` - 主程序打包脚本（已更新）
- ✅ `打包机器码检测工具.py` - 机器码检测工具打包脚本（新增）
- ✅ `main.spec` - PyInstaller配置文件（已更新）

### 测试文件
- ✅ `test_authorization.py` - 授权系统测试脚本（已更新）
- ✅ `test_machine_code.py` - 机器码生成测试脚本

## 🚀 完整使用流程

### 步骤1：获取机器码
在目标机器上运行：
```bash
# 方式1：运行Python脚本
python machine_code_detector.py

# 方式2：运行打包后的exe（推荐）
python 打包机器码检测工具.py  # 先打包
./dist_detector/机器码检测工具.exe  # 再运行
```

### 步骤2：生成授权配置
运行授权配置生成工具：
```bash
python authorization_config_generator.py
```

在GUI界面中：
1. 输入机器码列表（每行一个）
2. 设置过期日期（例如：2025-12-31）
3. 设置描述信息（例如：正式用户）
4. 点击"生成配置"
5. 复制生成的代码

### 步骤3：更新授权配置
将生成的代码替换到 `machine_code_verifier.py` 中的 `authorized_config`：

```python
# 预设的授权配置 - 包含过期时间
self.authorized_config = {
    "F168DDA7BD43EF480A7A2A2782F2D248": {
        "expire_date": "2025-12-31",
        "description": "用户A"
    },
    "另一个授权码": {
        "expire_date": "2025-06-30",
        "description": "用户B"
    },
    # 更多配置...
}
```

### 步骤4：测试验证
运行测试确保一切正常：
```bash
python test_authorization.py
```

### 步骤5：打包分发
```bash
python 打包用package.py
```

## 🔒 安全特性详解

### 1. 机器码生成
- **多重硬件信息**：主板、CPU、硬盘序列号
- **SHA-256哈希**：生成32位唯一标识
- **跨平台支持**：Windows/Linux/Mac

### 2. 授权码转换算法
```
机器码 → 加盐 → 反转 → 插入字符 → 再次加盐 → 哈希算法 → 大写输出
```

### 3. 时间验证机制
- **网络时间同步**：从多个时间服务器获取准确时间
- **防篡改检测**：检测本地时间是否被修改
- **离线容错**：网络断开时使用历史记录验证
- **异常代码**：统一的错误提示格式

### 4. 数据保护
- **加密存储**：时间记录使用加密存储
- **哈希校验**：HMAC-SHA256防篡改
- **隐藏窗口**：所有subprocess调用隐藏CMD窗口

## 🕒 时间限制功能

### 异常代码说明
- **授权异常 XXXA** - 授权时间已过期
- **授权异常 XXXB** - 检测到本地时间篡改
- **授权异常 XXXC** - 无有效时间记录
- **授权异常 XXXD** - 所有时间服务器无法访问

### 过期提醒
- 授权到期前7天会显示提醒
- 显示剩余天数和具体过期日期

## 📋 管理建议

### 1. 用户管理
- 为每个用户设置合理的过期时间
- 建立用户-机器码对应表
- 定期检查和更新授权状态

### 2. 安全管理
- 定期更换盐值密钥
- 监控异常授权尝试
- 备份授权配置

### 3. 技术维护
- 定期检查时间服务器可用性
- 更新时间验证逻辑
- 优化网络请求性能

## 🛠️ 工具使用

### 机器码检测工具
- **功能**：获取当前机器的唯一机器码
- **特点**：可独立打包，界面简洁
- **输出**：仅显示机器码，无敏感信息

### 机器码转换工具
- **功能**：将机器码转换为授权码
- **特点**：支持单个和批量转换
- **输出**：格式化的代码片段

### 授权配置生成工具
- **功能**：批量生成带时间限制的授权配置
- **特点**：可视化界面，支持批量操作
- **输出**：完整的Python代码配置

## ⚠️ 注意事项

### 部署前检查
1. 确保所有目标机器的机器码已收集
2. 验证授权配置的正确性
3. 测试时间验证功能
4. 检查网络连接要求

### 用户使用
1. 首次使用需要网络连接进行时间同步
2. 长期离线使用有7天限制
3. 不要修改系统时间
4. 授权即将过期时及时联系续期

### 技术限制
1. 依赖网络获取准确时间
2. 硬件更换会导致机器码变化
3. 虚拟机环境可能不稳定
4. 需要足够的系统权限获取硬件信息

## 🎉 总结

现在你拥有了一个功能完整、安全可靠的授权系统：

- ✅ **无CMD闪烁** - 用户体验友好
- ✅ **授权码复杂验证** - 安全性大幅提升
- ✅ **时间限制控制** - 精细的授权管理
- ✅ **防篡改机制** - 多重安全保护
- ✅ **工具链完整** - 从检测到配置一站式解决
- ✅ **易于管理** - 可视化配置生成工具

可以放心打包分发，享受强大的软件保护功能！
