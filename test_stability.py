#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序稳定性和进度条显示
"""

import sys
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QProgressBar, QTextEdit, QLabel
from PySide6.QtCore import QTimer

class StabilityTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("程序稳定性测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("程序稳定性和进度条测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试按钮
        self.test_button = QPushButton("开始稳定性测试")
        self.test_button.clicked.connect(self.start_stability_test)
        layout.addWidget(self.test_button)
        
        # 进度条测试按钮
        self.progress_button = QPushButton("测试进度条显示")
        self.progress_button.clicked.connect(self.test_progress_bar)
        layout.addWidget(self.progress_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_test)
        self.test_step = 0
        self.max_steps = 10
        
    def start_stability_test(self):
        """开始稳定性测试"""
        self.test_button.setEnabled(False)
        self.log_text.clear()
        self.log_text.append("🚀 开始稳定性测试...")
        
        # 模拟一些操作
        for i in range(5):
            self.log_text.append(f"✅ 测试步骤 {i+1}: 程序运行正常")
            QApplication.processEvents()  # 处理事件，保持界面响应
            
        self.log_text.append("🎉 稳定性测试完成 - 程序运行稳定！")
        self.test_button.setEnabled(True)
        
    def test_progress_bar(self):
        """测试进度条显示"""
        self.progress_button.setEnabled(False)
        self.log_text.clear()
        self.log_text.append("📊 开始进度条测试...")
        
        self.test_step = 0
        self.progress_bar.setValue(0)
        
        # 模拟混剪阶段（占总进度的50%）
        self.log_text.append("🎬 模拟混剪阶段...")
        self.simulate_mixing_progress()
        
    def simulate_mixing_progress(self):
        """模拟混剪进度"""
        # 假设有2个扩展功能，总共3个步骤
        total_steps = 3  # 混剪 + 2个扩展功能
        step_progress = 100 / total_steps
        
        # 模拟混剪进度（第1步）
        for i in range(11):  # 0-100%
            mixing_progress = i * 10
            # 混剪只占总进度的1/3
            total_progress = int((mixing_progress / 100) * step_progress)
            self.progress_bar.setValue(total_progress)
            self.log_text.append(f"混剪进度: {mixing_progress}% (总进度: {total_progress}%)")
            QApplication.processEvents()
            time.sleep(0.1)
            
        # 混剪完成，开始扩展功能
        self.log_text.append("✅ 混剪完成，开始扩展功能...")
        
        # 模拟扩展功能1（第2步）
        current_step = 1
        base_progress = int(current_step * step_progress)
        self.progress_bar.setValue(base_progress)
        self.log_text.append(f"🔧 扩展功能1完成 (总进度: {base_progress}%)")
        QApplication.processEvents()
        time.sleep(0.5)
        
        # 模拟扩展功能2（第3步）
        current_step = 2
        base_progress = int(current_step * step_progress)
        self.progress_bar.setValue(base_progress)
        self.log_text.append(f"🔧 扩展功能2完成 (总进度: {base_progress}%)")
        QApplication.processEvents()
        time.sleep(0.5)
        
        # 全部完成
        self.progress_bar.setValue(100)
        self.log_text.append("🎉 所有任务完成！(总进度: 100%)")
        self.log_text.append("✅ 进度条测试完成 - 显示正常！")
        
        self.progress_button.setEnabled(True)
        
    def update_test(self):
        """更新测试进度"""
        self.test_step += 1
        progress = int((self.test_step / self.max_steps) * 100)
        self.progress_bar.setValue(progress)
        self.log_text.append(f"测试进度: {progress}%")
        
        if self.test_step >= self.max_steps:
            self.timer.stop()
            self.log_text.append("测试完成！")
            self.test_button.setEnabled(True)

def main():
    app = QApplication(sys.argv)
    
    window = StabilityTestWindow()
    window.show()
    
    # 显示测试说明
    window.log_text.append("📋 测试说明:")
    window.log_text.append("1. 点击'开始稳定性测试'检查程序是否稳定运行")
    window.log_text.append("2. 点击'测试进度条显示'检查进度条是否正常显示")
    window.log_text.append("3. 观察是否有闪退或异常情况")
    window.log_text.append("")
    window.log_text.append("✅ 程序已启动，可以开始测试...")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
